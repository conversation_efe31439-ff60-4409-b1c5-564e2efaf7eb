# Weather Project

## 项目概述
本项目是一个基于 Java 和 Spring Boot 的天气信息处理系统，集成了 Orekit 库用于太阳位置计算，并通过第三方 API 获取天气数据。项目支持动态数据源配置、JWT 认证以及天气数据的存储和更新。

## 主要功能
1. **天气数据获取**：
   - 使用第三方天气 API 获取未来 24 小时的天气信息。
   - 支持多城市天气数据的批量处理。
2. **太阳位置计算**：
   - 基于 Orekit 库计算太阳的方位角和高度角。
   - 提供精确的地理位置和时间输入支持。
3. **JWT 认证**：
   - 使用 EdDSA 算法生成 JWT，用于 API 请求的身份验证。
4. **动态数据源**：
   - 集成 MyBatis-Plus 动态数据源，支持多数据库操作。

## 技术栈
- **编程语言**: Java 8
- **框架**: Spring Boot 2.7.4
- **构建工具**: Maven
- **关键依赖**:
  - Orekit (12.0.1): 用于天文计算。
  - MyBatis-Plus (3.5.2): 数据库操作。
  - EdDSA (0.3.0): 用于 JWT 签名。
  - Dynamic Datasource (3.5.0): 动态数据源支持。
- **其他工具**:
  - Hutool: HTTP 请求处理。
  - FastJSON: JSON 数据解析。